"use client";

import { useState } from "react";
import {
  ArrowLeft,
  DollarSign,
  Calendar,
  FileText,
  Users,
  CheckCircle,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface VehicleData {
  listingType: "rental" | "fractional" | "";
  rentalPurpose?: "business" | "individual";
  rentalPeriod?: "daily" | "weekly" | "monthly";
  ownershipPercentage?: string;
  allowPartialPurchase?: boolean;
  minimumPurchasePercentage?: string;
  make: string;
  model: string;
  year: string;
  color: string;
  mileage: string;
  condition: string;
  images: File[];
  documents: any[];
}

interface FinancialTerms {
  // For rental listings
  rentalRate?: string;
  rentalPeriod?: string;
  depositAmount?: string;
  depositRequired?: boolean;

  // For fractional ownership listings
  totalPrice?: string;
  minimumPurchaseAmount?: string;

  // Legacy fields (for e-hailing compatibility)
  weeklyRate?: string;
  initiationFee?: string;
  ownershipTimeline?: string;
}

interface ApplicantPreferences {
  minimumAge?: string;
  genderPreference?: "male" | "female" | "no-preference";
  minimumDrivingExperience?: string;
  experienceRequired?: boolean;
  experienceQuestions?: string[];
  requiredDocuments: string[];
}

interface ListingTermsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (terms: FinancialTerms, preferences: ApplicantPreferences) => void;
  vehicleData: VehicleData | null;
}

export default function ListingTermsDrawer({
  isOpen,
  onClose,
  onSubmit,
  vehicleData,
}: ListingTermsDrawerProps) {
  const [currentStep, setCurrentStep] = useState<"financial" | "requirements">(
    "financial"
  );

  const [financialTerms, setFinancialTerms] = useState<FinancialTerms>({
    // Initialize based on listing type
    rentalRate: "",
    rentalPeriod: "",
    depositAmount: "",
    depositRequired: false,
    totalPrice: "",
    minimumPurchaseAmount: "",
    // Legacy fields
    weeklyRate: "",
    initiationFee: "",
    ownershipTimeline: "36",
  });

  const [applicantPreferences, setApplicantPreferences] =
    useState<ApplicantPreferences>({
      minimumAge: "",
      genderPreference: "no-preference",
      minimumDrivingExperience: "",
      experienceRequired: false,
      experienceQuestions: [],
      requiredDocuments: ["ID Document", "Driver's License"],
    });

  const availableDocuments = [
    "ID Document",
    "Driver's License",
    "Proof of Address",
    "Bank Statement - 3 Months",
    "PrDP (Professional Driving Permit)",
    "Operator Card",
  ];

  const getExperienceQuestions = () => {
    if (vehicleData?.listingType === "rental") {
      return [
        "Do you have experience with vehicle rentals?",
        "What is your intended use for the vehicle?",
      ];
    } else if (vehicleData?.listingType === "fractional") {
      return ["Have you participated in shared ownership before?"];
    } else {
      // Legacy e-hailing questions
      return [
        "Which e-hailing company do you currently drive for?",
        "How long have you been driving for e-hailing?",
        "What are your profile numbers/ratings?",
      ];
    }
  };

  const handleFinancialChange = (
    field: keyof FinancialTerms,
    value: string | boolean
  ) => {
    setFinancialTerms((prev) => ({ ...prev, [field]: value }));
  };

  const handleDocumentToggle = (document: string) => {
    setApplicantPreferences((prev: ApplicantPreferences) => ({
      ...prev,
      requiredDocuments: prev.requiredDocuments.includes(document)
        ? prev.requiredDocuments.filter((doc: string) => doc !== document)
        : [...prev.requiredDocuments, document],
    }));
  };

  const handleExperienceToggle = () => {
    setApplicantPreferences((prev: ApplicantPreferences) => ({
      ...prev,
      experienceRequired: !prev.experienceRequired,
      experienceQuestions: !prev.experienceRequired
        ? getExperienceQuestions()
        : [],
    }));
  };

  const handlePreferenceChange = (
    field: keyof ApplicantPreferences,
    value: string | boolean
  ) => {
    setApplicantPreferences((prev: ApplicantPreferences) => ({
      ...prev,
      [field]: value,
    }));
  };

  const canProceedFromFinancial = () => {
    if (!vehicleData?.listingType) return false;

    if (vehicleData.listingType === "rental") {
      return financialTerms.rentalRate && financialTerms.rentalPeriod;
    } else if (vehicleData.listingType === "fractional") {
      return (
        financialTerms.totalPrice &&
        (!vehicleData.allowPartialPurchase ||
          financialTerms.minimumPurchaseAmount)
      );
    }

    // Legacy e-hailing validation
    return (
      financialTerms.weeklyRate &&
      financialTerms.initiationFee &&
      financialTerms.depositAmount &&
      financialTerms.ownershipTimeline
    );
  };

  const canSubmit = () => {
    return applicantPreferences.requiredDocuments.length >= 2; // At least ID and License
  };

  const handleNext = () => {
    if (currentStep === "financial" && canProceedFromFinancial()) {
      setCurrentStep("requirements");
    } else if (currentStep === "requirements" && canSubmit()) {
      onSubmit(financialTerms, applicantPreferences);
    }
  };

  const handleBack = () => {
    if (currentStep === "requirements") {
      setCurrentStep("financial");
    } else {
      onClose();
    }
  };

  // Return early if no vehicle data
  if (!vehicleData) {
    return null;
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                title="Back"
                onClick={handleBack}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  {currentStep === "financial"
                    ? "Financial Terms"
                    : "Applicant Preferences"}
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  {currentStep === "financial"
                    ? "Set your rates and terms"
                    : "Define preferences for potential applicants"}
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Progress Indicator */}
          <div className="px-6 py-3 bg-gray-50 border-b">
            <div className="flex items-center justify-between text-xs text-gray-600">
              <span
                className={
                  currentStep === "financial"
                    ? "text-[#009639] font-medium"
                    : ""
                }
              >
                Financial Terms
              </span>
              <span
                className={
                  currentStep === "requirements"
                    ? "text-[#009639] font-medium"
                    : ""
                }
              >
                Preferences
              </span>
            </div>
            <div className="mt-2 h-1 bg-gray-200 rounded-full">
              <div
                className="h-1 bg-[#009639] rounded-full transition-all duration-300"
                style={{ width: currentStep === "financial" ? "50%" : "100%" }}
              />
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {/* Vehicle Summary */}
            <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4 mb-6">
              <h4 className="font-semibold text-[#333333] mb-2">
                Your Vehicle
              </h4>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-[#333333]">
                    {vehicleData.make} {vehicleData.model}
                  </p>
                  <p className="text-xs text-[#797879]">
                    {vehicleData.year} • {vehicleData.color} •{" "}
                    {Number(vehicleData.mileage).toLocaleString()}km
                  </p>
                </div>
                <span className="text-xs bg-[#e6ffe6] text-[#009639] px-2 py-1 rounded-full font-medium">
                  {vehicleData.condition}
                </span>
              </div>
            </div>

            {currentStep === "financial" && (
              <div className="space-y-6">
                {/* Pricing & Terms */}
                <div>
                  <div className="flex items-center mb-3">
                    <DollarSign size={18} className="mr-2 text-[#009639]" />
                    <h4 className="font-semibold text-[#333333]">
                      Pricing & Terms
                    </h4>
                  </div>
                  <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                    <div className="space-y-4">
                      {/* Rental Listing Fields */}
                      {vehicleData.listingType === "rental" && (
                        <>
                          <div>
                            <label className="text-[#797879] text-xs mb-1 block">
                              Rental Rate (R)
                            </label>
                            <input
                              type="number"
                              value={financialTerms.rentalRate}
                              onChange={(e) =>
                                handleFinancialChange(
                                  "rentalRate",
                                  e.target.value
                                )
                              }
                              className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                              placeholder={
                                vehicleData.rentalPeriod === "daily"
                                  ? "e.g. 500 (per day)"
                                  : vehicleData.rentalPeriod === "weekly"
                                    ? "e.g. 2700 (per week)"
                                    : "e.g. 8000 (per month)"
                              }
                            />
                          </div>

                          <div>
                            <label className="text-[#797879] text-xs mb-1 block">
                              Rental Period
                            </label>
                            <input
                              type="text"
                              value={financialTerms.rentalPeriod}
                              onChange={(e) =>
                                handleFinancialChange(
                                  "rentalPeriod",
                                  e.target.value
                                )
                              }
                              className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                              placeholder={
                                vehicleData.rentalPeriod === "daily"
                                  ? "e.g. per day"
                                  : vehicleData.rentalPeriod === "weekly"
                                    ? "e.g. per week"
                                    : "e.g. per month"
                              }
                            />
                          </div>

                          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                              <p className="text-sm font-medium text-[#333333]">
                                Require Security Deposit
                              </p>
                              <p className="text-xs text-[#797879]">
                                Optional deposit to secure rental
                              </p>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                title="Deposit Required"
                                type="checkbox"
                                className="sr-only peer"
                                checked={financialTerms.depositRequired}
                                onChange={(e) =>
                                  handleFinancialChange(
                                    "depositRequired",
                                    e.target.checked
                                  )
                                }
                              />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                            </label>
                          </div>

                          {financialTerms.depositRequired && (
                            <div>
                              <label className="text-[#797879] text-xs mb-1 block">
                                Deposit Amount (R)
                              </label>
                              <input
                                type="number"
                                value={financialTerms.depositAmount}
                                onChange={(e) =>
                                  handleFinancialChange(
                                    "depositAmount",
                                    e.target.value
                                  )
                                }
                                className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                                placeholder="e.g. 5000"
                              />
                            </div>
                          )}
                        </>
                      )}

                      {/* Fractional Ownership Fields */}
                      {vehicleData.listingType === "fractional" && (
                        <>
                          <div>
                            <label className="text-[#797879] text-xs mb-1 block">
                              Total Price for {vehicleData.ownershipPercentage}%
                              Ownership (R)
                            </label>
                            <input
                              type="number"
                              value={financialTerms.totalPrice}
                              onChange={(e) =>
                                handleFinancialChange(
                                  "totalPrice",
                                  e.target.value
                                )
                              }
                              className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                              placeholder="e.g. 150000"
                            />
                          </div>

                          {vehicleData.allowPartialPurchase && (
                            <div>
                              <label className="text-[#797879] text-xs mb-1 block">
                                Minimum Purchase Amount (R)
                              </label>
                              <input
                                type="number"
                                value={financialTerms.minimumPurchaseAmount}
                                onChange={(e) =>
                                  handleFinancialChange(
                                    "minimumPurchaseAmount",
                                    e.target.value
                                  )
                                }
                                className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                                placeholder="e.g. 25000"
                              />
                              <p className="text-xs text-[#797879] mt-1">
                                Minimum amount for partial purchases (based on{" "}
                                {vehicleData.minimumPurchasePercentage}%
                                minimum)
                              </p>
                            </div>
                          )}
                        </>
                      )}

                      {/* Legacy E-hailing Fields (fallback) */}
                      {!vehicleData.listingType && (
                        <>
                          <div>
                            <label className="text-[#797879] text-xs mb-1 block">
                              Weekly Rate (R)
                            </label>
                            <input
                              type="number"
                              value={financialTerms.weeklyRate}
                              onChange={(e) =>
                                handleFinancialChange(
                                  "weeklyRate",
                                  e.target.value
                                )
                              }
                              className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                              placeholder="e.g. 2700"
                            />
                          </div>

                          <div>
                            <label className="text-[#797879] text-xs mb-1 block">
                              Initiation Fee (R)
                            </label>
                            <input
                              type="number"
                              value={financialTerms.initiationFee}
                              onChange={(e) =>
                                handleFinancialChange(
                                  "initiationFee",
                                  e.target.value
                                )
                              }
                              className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                              placeholder="e.g. 7500"
                            />
                          </div>

                          <div>
                            <label className="text-[#797879] text-xs mb-1 block">
                              Security Deposit (R)
                            </label>
                            <input
                              type="number"
                              value={financialTerms.depositAmount}
                              onChange={(e) =>
                                handleFinancialChange(
                                  "depositAmount",
                                  e.target.value
                                )
                              }
                              className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                              placeholder="e.g. 5000"
                            />
                          </div>

                          <div>
                            <label className="text-[#797879] text-xs mb-1 block">
                              Path to Ownership (months)
                            </label>
                            <select
                              title="Path to Ownership"
                              value={financialTerms.ownershipTimeline}
                              onChange={(e) =>
                                handleFinancialChange(
                                  "ownershipTimeline",
                                  e.target.value
                                )
                              }
                              className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                            >
                              <option value="24">24 months</option>
                              <option value="36">36 months</option>
                              <option value="48">48 months</option>
                              <option value="60">60 months</option>
                            </select>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <p className="text-xs text-blue-800">
                    <strong>Tip:</strong>{" "}
                    {vehicleData.listingType === "rental"
                      ? "Competitive rental rates attract more qualified renters. Consider market rates and your vehicle's condition when setting prices."
                      : vehicleData.listingType === "fractional"
                        ? "Fair pricing attracts serious co-ownership buyers. Research similar vehicles and consider your vehicle's condition and market value."
                        : "Competitive rates attract more qualified lessees. Consider market rates and your vehicle's condition when setting prices."}
                  </p>
                </div>

                {/* What Happens Next */}
                <div>
                  <div className="flex items-center mb-3">
                    <Calendar size={18} className="mr-2 text-[#009639]" />
                    <h4 className="font-semibold text-[#333333]">
                      What Happens Next
                    </h4>
                  </div>
                  <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                    <div className="space-y-3">
                      {(vehicleData.listingType === "rental"
                        ? [
                            "Submit rental listing for review",
                            "Document verification",
                            "Vehicle inspection scheduled",
                            "Listing goes live",
                            "Receive rental applications",
                            "Select qualified renters",
                          ]
                        : vehicleData.listingType === "fractional"
                          ? [
                              "Submit ownership listing for review",
                              "Document verification",
                              "Vehicle valuation assessment",
                              "Listing goes live",
                              "Receive purchase applications",
                              "Select co-owners",
                            ]
                          : [
                              "Submit listing for review",
                              "Document verification",
                              "Vehicle inspection scheduled",
                              "Listing goes live",
                              "Receive lessee applications",
                              "Select qualified lessees",
                            ]
                      ).map((step, index) => (
                        <div
                          key={index}
                          className="flex items-center text-sm text-[#797879]"
                        >
                          <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium">
                            {index + 1}
                          </div>
                          {step}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {currentStep === "requirements" && (
              <div className="space-y-6">
                {/* Applicant Preferences */}
                <div>
                  <div className="flex items-center mb-3">
                    <Users size={18} className="mr-2 text-[#009639]" />
                    <h4 className="font-semibold text-[#333333]">
                      Applicant Preferences
                    </h4>
                  </div>

                  {/* Basic Preferences */}
                  <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4 mb-4">
                    <div className="space-y-4">
                      <div>
                        <label className="text-[#797879] text-xs mb-1 block">
                          Minimum Age (Optional)
                        </label>
                        <input
                          type="number"
                          value={applicantPreferences.minimumAge}
                          onChange={(e) =>
                            handlePreferenceChange("minimumAge", e.target.value)
                          }
                          className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                          placeholder="e.g. 25"
                          min="18"
                          max="65"
                        />
                      </div>

                      <div>
                        <label className="text-[#797879] text-xs mb-1 block">
                          Gender Preference (Optional)
                        </label>
                        <select
                          title="Gender Preference"
                          value={applicantPreferences.genderPreference}
                          onChange={(e) =>
                            handlePreferenceChange(
                              "genderPreference",
                              e.target.value as
                                | "male"
                                | "female"
                                | "no-preference"
                            )
                          }
                          className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                        >
                          <option value="no-preference">No Preference</option>
                          <option value="male">Male</option>
                          <option value="female">Female</option>
                        </select>
                      </div>

                      <div>
                        <label className="text-[#797879] text-xs mb-1 block">
                          Minimum Driving Experience (Years)
                        </label>
                        <input
                          type="number"
                          value={applicantPreferences.minimumDrivingExperience}
                          onChange={(e) =>
                            handlePreferenceChange(
                              "minimumDrivingExperience",
                              e.target.value
                            )
                          }
                          className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                          placeholder="e.g. 3"
                          min="0"
                          max="50"
                        />
                      </div>
                    </div>
                  </div>
                  <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="text-sm font-medium text-[#333333]">
                            Require Additional Experience Questions
                          </p>
                          <p className="text-xs text-[#797879]">
                            Ask applicants about their relevant experience
                          </p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            title="Experience Required"
                            type="checkbox"
                            className="sr-only peer"
                            checked={applicantPreferences.experienceRequired}
                            onChange={handleExperienceToggle}
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                        </label>
                      </div>

                      {applicantPreferences.experienceRequired && (
                        <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                          <p className="text-xs text-green-800 mb-2">
                            <strong>
                              Experience questions that will be asked:
                            </strong>
                          </p>
                          <ul className="text-xs text-green-700 space-y-1">
                            {getExperienceQuestions().map(
                              (question: string, index: number) => (
                                <li key={index}>• {question}</li>
                              )
                            )}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Application Process */}
                <div>
                  <div className="flex items-center mb-3">
                    <Users size={18} className="mr-2 text-[#009639]" />
                    <h4 className="font-semibold text-[#333333]">
                      {vehicleData.listingType === "rental"
                        ? "Rental Application Process"
                        : vehicleData.listingType === "fractional"
                          ? "Co-ownership Application Process"
                          : "Application Process"}
                    </h4>
                  </div>
                  <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                    <div className="space-y-3">
                      {(vehicleData.listingType === "rental"
                        ? [
                            "Qualified renters apply",
                            "Review applications & documents",
                            "Interview potential renters",
                            "Select preferred renter",
                            "Sign rental agreement",
                            "Vehicle handover & start earning",
                          ]
                        : vehicleData.listingType === "fractional"
                          ? [
                              "Interested buyers apply",
                              "Review applications & documents",
                              "Negotiate ownership terms",
                              "Select co-owners",
                              "Sign ownership agreement",
                              "Complete transaction & share ownership",
                            ]
                          : [
                              "Qualified drivers apply",
                              "Review applications & documents",
                              "Interview potential lessees",
                              "Select preferred lessee",
                              "Sign lease agreement",
                              "Vehicle handover & start earning",
                            ]
                      ).map((step, index) => (
                        <div
                          key={index}
                          className="flex items-center text-sm text-[#797879]"
                        >
                          <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium">
                            {index + 1}
                          </div>
                          {step}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4">
            <button
              onClick={handleNext}
              disabled={
                (currentStep === "financial" && !canProceedFromFinancial()) ||
                (currentStep === "requirements" && !canSubmit())
              }
              className={`w-full py-3 rounded-full font-semibold transition-all ${
                (currentStep === "financial" && canProceedFromFinancial()) ||
                (currentStep === "requirements" && canSubmit())
                  ? "bg-[#009639] text-white hover:bg-[#007A2F]"
                  : "bg-gray-200 text-gray-400 cursor-not-allowed"
              }`}
            >
              {currentStep === "financial"
                ? "Continue to Preferences"
                : "Submit for Approval"}
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
