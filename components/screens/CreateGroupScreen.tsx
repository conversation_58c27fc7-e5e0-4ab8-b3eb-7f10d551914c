"use client";

import { useState, useEffect } from "react";
import { ArrowLeft, Plus, X, Users, Building, Car } from "lucide-react";
import { useNavigation } from "@/hooks/useNavigation";
import { useCurrentUser } from "@/hooks/use-current-user";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PageWithScroll } from "@/components/ui/scroll-container";
import {
  createGroup,
  getCities,
  getCountriesWithIds,
  getVehicleModels,
} from "@/drizzle-actions/groups";
import { CompanyPurposeEnum, GroupRoleEnum } from "@/types/groups";

interface City {
  id: number;
  name: string;
  province: string;
  country: string;
}

interface Country {
  id: number;
  name: string;
  code: string;
}

interface MemberInvitation {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: GroupRoleEnum;
}

interface VehicleModel {
  id: number;
  model: string;
  year_model: number;
  make_name: string;
}

interface CreateGroupScreenProps {
  params?: Record<string, any>;
}

export default function CreateGroupScreen({ params }: CreateGroupScreenProps) {
  const { goBack, navigateToGroupDetails } = useNavigation();
  const { partyId: currentUserPartyId, isLoading: userLoading } = useCurrentUser();
  
  // Group Information
  const [groupName, setGroupName] = useState("");
  const [description, setDescription] = useState("");
  const [purpose, setPurpose] = useState<CompanyPurposeEnum>(CompanyPurposeEnum.OTHER);
  const [isManaged, setIsManaged] = useState(false);
  const [selectedCityId, setSelectedCityId] = useState<number | undefined>();
  const [selectedCountryId, setSelectedCountryId] = useState<number>(1); // Default to South Africa
  
  // Company Information
  const [createCompany, setCreateCompany] = useState(false);
  const [companyName, setCompanyName] = useState("");
  const [registrationNumber, setRegistrationNumber] = useState("");
  const [registrationCountry, setRegistrationCountry] = useState("South Africa");
  
  // Vehicle Information (Required)
  const [vinNumber, setVinNumber] = useState("");
  const [vehicleRegistration, setVehicleRegistration] = useState("");
  const [manufacturingYear, setManufacturingYear] = useState<number>(new Date().getFullYear());
  const [color, setColor] = useState("");
  const [selectedModelId, setSelectedModelId] = useState<number | undefined>();
  
  // Member Invitations
  const [memberInvitations, setMemberInvitations] = useState<MemberInvitation[]>([]);
  const [newMemberEmail, setNewMemberEmail] = useState("");
  const [newMemberFirstName, setNewMemberFirstName] = useState("");
  const [newMemberLastName, setNewMemberLastName] = useState("");
  const [newMemberRole, setNewMemberRole] = useState<GroupRoleEnum>(GroupRoleEnum.MEMBER);
  
  // Data and State
  const [cities, setCities] = useState<City[]>([]);
  const [countries, setCountries] = useState<Country[]>([]);
  const [vehicleModels, setVehicleModels] = useState<VehicleModel[]>([]);
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch all data in parallel
        const [cityList, countryList, vehicleModelList] = await Promise.all([
          getCities(),
          getCountriesWithIds(),
          getVehicleModels(),
        ]);
        
        setCities(cityList);
        setCountries(countryList);
        setVehicleModels(vehicleModelList);
        
      } catch (error) {
        console.error("Failed to fetch data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const addMemberInvitation = () => {
    if (
      !newMemberEmail.trim() ||
      !newMemberFirstName.trim() ||
      !newMemberLastName.trim()
    ) {
      alert("Please fill in all member details");
      return;
    }

    if (memberInvitations.some((m) => m.email === newMemberEmail)) {
      alert("This email is already in the invitation list");
      return;
    }

    const newInvitation: MemberInvitation = {
      id: Date.now().toString(),
      email: newMemberEmail.trim(),
      firstName: newMemberFirstName.trim(),
      lastName: newMemberLastName.trim(),
      role: newMemberRole,
    };

    setMemberInvitations([...memberInvitations, newInvitation]);
    setNewMemberEmail("");
    setNewMemberFirstName("");
    setNewMemberLastName("");
    setNewMemberRole(GroupRoleEnum.MEMBER);
  };

  const removeMemberInvitation = (id: string) => {
    setMemberInvitations(memberInvitations.filter((m) => m.id !== id));
  };

  const handleCreateGroup = async () => {
    if (!currentUserPartyId) {
      alert("User not authenticated");
      return;
    }

    if (!groupName.trim()) {
      alert("Please enter a group name");
      return;
    }

    if (!description.trim()) {
      alert("Please enter a description");
      return;
    }

    if (!selectedCityId) {
      alert("Please select a city");
      return;
    }

    if (!vinNumber.trim()) {
      alert("Please enter a VIN number");
      return;
    }

    if (!selectedModelId) {
      alert("Please select a vehicle model");
      return;
    }

    try {
      setCreating(true);

      const groupData = {
        groupCreate: {
          name: groupName.trim(),
          description: description.trim(),
          cityId: selectedCityId,
          countryId: selectedCountryId,
          InitialPurpose: purpose,
          isManaged: isManaged,
          createdBy: currentUserPartyId,
        },
        memberInvitations: memberInvitations.length > 0 ? memberInvitations.map(invite => ({
          firstName: invite.firstName,
          lastName: invite.lastName,
          email: invite.email,
          role: invite.role,
        })) : undefined,
        vehicleCreate: {
          model_id: selectedModelId,
          vin_number: vinNumber.trim(),
          vehicle_registration: vehicleRegistration.trim() || undefined,
          country_id: selectedCountryId,
          manufacturing_year: manufacturingYear,
          color: color.trim() || undefined,
          is_active: true,
        },
      };

      const result = await createGroup(groupData);

      if (result && result.group) {
        alert("Group created successfully!");
        navigateToGroupDetails(result.group.id.toString());
      } else {
        alert("Failed to create group");
      }
    } catch (error) {
      console.error("Error creating group:", error);
      alert("Failed to create group. Please try again.");
    } finally {
      setCreating(false);
    }
  };

  const purposeOptions = [
    { value: CompanyPurposeEnum.RIDE_SHARE, label: "Ride Share", description: "Share vehicles for transportation services" },
    { value: CompanyPurposeEnum.FLEET, label: "Fleet Management", description: "Manage a fleet of vehicles" },
    { value: CompanyPurposeEnum.GROUP_MONETIZATION, label: "Group Monetization", description: "Generate income through group activities" },
    { value: CompanyPurposeEnum.OTHER, label: "Other", description: "Other purposes not listed above" },
  ];

  const header = (
    <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
      <button className="mr-4" onClick={goBack}>
        <ArrowLeft size={24} className="text-white" />
      </button>
      <h1 className="text-xl font-bold text-white">Create Group</h1>
    </div>
  );

  if (loading) {
    return (
      <PageWithScroll header={header} className="bg-[#f5f5f5]" paddingBottom="pb-32">
        <div className="p-6">
          <div className="bg-white rounded-xl shadow-md p-6 text-center border border-gray-100">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mx-auto mb-4"></div>
            <p className="text-[#797879]">Loading form data...</p>
          </div>
        </div>
      </PageWithScroll>
    );
  }

  return (
    <PageWithScroll header={header} className="bg-[#f5f5f5]" paddingBottom="pb-32">
      <div className="p-6 space-y-6">
        {/* Basic Information */}
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center">
              <Users size={16} className="text-[#009639]" />
            </div>
            <h2 className="text-lg font-semibold text-[#333333]">Group Information</h2>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-[#333333] mb-2">Group Name *</label>
              <input
                type="text"
                value={groupName}
                onChange={(e) => setGroupName(e.target.value)}
                placeholder="Enter group name"
                className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-[#333333] mb-2">Description *</label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe your group's purpose and goals"
                rows={3}
                className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-[#333333] mb-2">Purpose</label>
              <select
                value={purpose}
                onChange={(e) => setPurpose(e.target.value as CompanyPurposeEnum)}
                className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              >
                {purposeOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[#333333] mb-2">Country</label>
                <select
                  value={selectedCountryId}
                  onChange={(e) => setSelectedCountryId(Number(e.target.value))}
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                >
                  {countries.map((country) => (
                    <option key={country.id} value={country.id}>
                      {country.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-[#333333] mb-2">City *</label>
                <select
                  value={selectedCityId || ''}
                  onChange={(e) => setSelectedCityId(Number(e.target.value) || undefined)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                >
                  <option value="">Select a city</option>
                  {cities
                    .filter((city) => city.id) // Filter cities for selected country if needed
                    .map((city) => (
                      <option key={city.id} value={city.id}>
                        {city.name}, {city.province}
                      </option>
                    ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Vehicle Information */}
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center">
              <Car size={16} className="text-[#009639]" />
            </div>
            <h2 className="text-lg font-semibold text-[#333333]">Vehicle Information</h2>
            <Badge className="bg-red-100 text-red-800">Required</Badge>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-[#333333] mb-2">Vehicle Model *</label>
              <select
                value={selectedModelId || ''}
                onChange={(e) => setSelectedModelId(Number(e.target.value) || undefined)}
                className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              >
                <option value="">Select a vehicle model</option>
                {vehicleModels.map((model) => (
                  <option key={model.id} value={model.id}>
                    {model.make_name} {model.model} ({model.year_model})
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-[#333333] mb-2">VIN Number *</label>
              <input
                type="text"
                value={vinNumber}
                onChange={(e) => setVinNumber(e.target.value)}
                placeholder="Enter VIN number"
                className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[#333333] mb-2">Registration</label>
                <input
                  type="text"
                  value={vehicleRegistration}
                  onChange={(e) => setVehicleRegistration(e.target.value)}
                  placeholder="License plate"
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-[#333333] mb-2">Year</label>
                <input
                  type="number"
                  value={manufacturingYear}
                  onChange={(e) => setManufacturingYear(Number(e.target.value))}
                  min="1990"
                  max={new Date().getFullYear() + 1}
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-[#333333] mb-2">Color</label>
              <input
                type="text"
                value={color}
                onChange={(e) => setColor(e.target.value)}
                placeholder="Vehicle color"
                className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Member Invitations */}
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center">
              <Users size={16} className="text-[#009639]" />
            </div>
            <h2 className="text-lg font-semibold text-[#333333]">Invite Members</h2>
            <Badge className="bg-blue-100 text-blue-800">Optional</Badge>
          </div>
          
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[#333333] mb-2">First Name</label>
                <input
                  type="text"
                  value={newMemberFirstName}
                  onChange={(e) => setNewMemberFirstName(e.target.value)}
                  placeholder="First name"
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-[#333333] mb-2">Last Name</label>
                <input
                  type="text"
                  value={newMemberLastName}
                  onChange={(e) => setNewMemberLastName(e.target.value)}
                  placeholder="Last name"
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-[#333333] mb-2">Email</label>
              <input
                type="email"
                value={newMemberEmail}
                onChange={(e) => setNewMemberEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              />
            </div>
            
            <div className="flex gap-3">
              <select
                value={newMemberRole}
                onChange={(e) => setNewMemberRole(e.target.value as GroupRoleEnum)}
                className="flex-1 px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              >
                <option value={GroupRoleEnum.MEMBER}>Member</option>
                <option value={GroupRoleEnum.CO_OWNER}>Co-Owner</option>
              </select>
              <Button
                onClick={addMemberInvitation}
                className="bg-[#009639] hover:bg-[#007A2F] text-white"
              >
                <Plus size={16} className="mr-2" />
                Add Member
              </Button>
            </div>
            
            {/* Member List */}
            {memberInvitations.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-[#333333]">Members to Invite ({memberInvitations.length})</h4>
                {memberInvitations.map((member) => (
                  <div key={member.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-[#333333]">{member.firstName} {member.lastName}</p>
                      <p className="text-sm text-[#797879]">{member.email} • {member.role}</p>
                    </div>
                    <button
                      onClick={() => removeMemberInvitation(member.id)}
                      className="p-1 text-red-600 hover:bg-red-100 rounded"
                    >
                      <X size={16} />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Create Button */}
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={goBack}
            className="flex-1"
            disabled={creating}
          >
            Cancel
          </Button>
          <Button
            onClick={handleCreateGroup}
            disabled={creating || !groupName.trim() || !description.trim() || !selectedCityId || !vinNumber.trim() || !selectedModelId}
            className="bg-[#009639] hover:bg-[#007A2F] text-white flex-1"
          >
            {creating ? "Creating..." : "Create Group"}
          </Button>
        </div>
      </div>
    </PageWithScroll>
  );
} 