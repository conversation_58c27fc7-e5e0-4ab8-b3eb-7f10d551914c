describe('Vehicle Listing Tests', () => {
  beforeEach(() => {
    cy.visit("/home");
    cy.fixture('user').as('user'); // Load the fixture and alias it
  });

  it("should complete vehicle listing flow", () => {
    // First login
    cy.contains(/Sign In/i).click();

    cy.get('@user').then((user) => {
      cy.get('form[data-amplify-authenticator-signin] input[name="username"]').type(user.email);
      cy.get('form[data-amplify-authenticator-signin] input[name="password"]').type(user.password);
      cy.get('form[data-amplify-authenticator-signin]')
        .find('button[type="submit"]')
        .click();

      // Wait for login to complete
      cy.contains(/Hi there/i).should('not.exist');
      
      // Switch to EARN mode
      cy.get('[data-testid="earn-toggle"]').click();
      
      // Click on "List Your Vehicle" button
      cy.contains(/List Your Vehicle/i).click();
      
      // Fill out vehicle listing form
      cy.get('select[name="listingType"]').select('rental');
      cy.get('input[name="make"]').type('Toyota');
      cy.get('input[name="model"]').type('Camry');
      cy.get('input[name="year"]').type('2020');
      cy.get('input[name="color"]').type('Silver');
      cy.get('input[name="mileage"]').type('50000');
      cy.get('select[name="condition"]').select('good');
      cy.get('select[name="rentalPeriod"]').select('daily');
      
      // Continue to next step
      cy.contains(/Continue to Preferences/i).click();
      
      // Fill out financial terms
      cy.get('input[name="rentalRate"]').type('50');
      cy.get('input[name="depositAmount"]').type('200');
      
      // Continue to requirements step
      cy.contains(/Continue/i).click();
      
      // Fill out applicant requirements
      cy.get('input[name="minimumAge"]').type('25');
      cy.get('select[name="genderPreference"]').select('no-preference');
      cy.get('input[name="minimumDrivingExperience"]').type('3');
      
      // Select required documents
      cy.get('input[type="checkbox"][value="ID"]').check();
      cy.get('input[type="checkbox"][value="License"]').check();
      
      // Submit the listing
      cy.contains(/Submit for Approval/i).click();
      
      // Verify success message
      cy.contains(/submitted for approval/i).should('be.visible');
      cy.contains(/We'll review your listing/i).should('be.visible');
    });
  });

  it("should handle fractional ownership listing", () => {
    // First login
    cy.contains(/Sign In/i).click();

    cy.get('@user').then((user) => {
      cy.get('form[data-amplify-authenticator-signin] input[name="username"]').type(user.email);
      cy.get('form[data-amplify-authenticator-signin] input[name="password"]').type(user.password);
      cy.get('form[data-amplify-authenticator-signin]')
        .find('button[type="submit"]')
        .click();

      // Wait for login to complete
      cy.contains(/Hi there/i).should('not.exist');
      
      // Switch to EARN mode
      cy.get('[data-testid="earn-toggle"]').click();
      
      // Click on "List Your Vehicle" button
      cy.contains(/List Your Vehicle/i).click();
      
      // Fill out vehicle listing form for fractional ownership
      cy.get('select[name="listingType"]').select('fractional');
      cy.get('input[name="make"]').type('BMW');
      cy.get('input[name="model"]').type('X5');
      cy.get('input[name="year"]').type('2022');
      cy.get('input[name="color"]').type('Black');
      cy.get('input[name="mileage"]').type('15000');
      cy.get('select[name="condition"]').select('excellent');
      cy.get('input[name="ownershipPercentage"]').type('25');
      
      // Continue to next step
      cy.contains(/Continue to Preferences/i).click();
      
      // Fill out financial terms for fractional ownership
      cy.get('input[name="totalPrice"]').type('75000');
      cy.get('input[name="minimumPurchaseAmount"]').type('18750');
      
      // Continue to requirements step
      cy.contains(/Continue/i).click();
      
      // Fill out applicant requirements
      cy.get('input[name="minimumAge"]').type('30');
      cy.get('select[name="genderPreference"]').select('no-preference');
      cy.get('input[name="minimumDrivingExperience"]').type('5');
      
      // Select required documents
      cy.get('input[type="checkbox"][value="ID"]').check();
      cy.get('input[type="checkbox"][value="License"]').check();
      cy.get('input[type="checkbox"][value="Income"]').check();
      
      // Submit the listing
      cy.contains(/Submit for Approval/i).click();
      
      // Verify success message
      cy.contains(/submitted for approval/i).should('be.visible');
      cy.contains(/We'll review your listing/i).should('be.visible');
    });
  });

  it("should validate required fields", () => {
    // First login
    cy.contains(/Sign In/i).click();

    cy.get('@user').then((user) => {
      cy.get('form[data-amplify-authenticator-signin] input[name="username"]').type(user.email);
      cy.get('form[data-amplify-authenticator-signin] input[name="password"]').type(user.password);
      cy.get('form[data-amplify-authenticator-signin]')
        .find('button[type="submit"]')
        .click();

      // Wait for login to complete
      cy.contains(/Hi there/i).should('not.exist');
      
      // Switch to EARN mode
      cy.get('[data-testid="earn-toggle"]').click();
      
      // Click on "List Your Vehicle" button
      cy.contains(/List Your Vehicle/i).click();
      
      // Try to continue without filling required fields
      cy.contains(/Continue to Preferences/i).click();
      
      // Should show validation errors
      cy.contains(/required/i).should('be.visible');
      
      // Fill minimum required fields
      cy.get('select[name="listingType"]').select('rental');
      cy.get('input[name="make"]').type('Honda');
      cy.get('input[name="model"]').type('Civic');
      cy.get('input[name="year"]').type('2019');
      cy.get('input[name="color"]').type('Blue');
      cy.get('input[name="mileage"]').type('75000');
      cy.get('select[name="condition"]').select('good');
      cy.get('select[name="rentalPeriod"]').select('weekly');
      
      // Now should be able to continue
      cy.contains(/Continue to Preferences/i).click();
      
      // Should be on financial terms step
      cy.contains(/Financial Terms/i).should('be.visible');
    });
  });
});
