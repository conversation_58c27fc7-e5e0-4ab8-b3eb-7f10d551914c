"use server";

/**
 * CREATE LISTINGS - New Comprehensive Listing Feature
 *
 * This file handles the creation of new vehicle listings using the h_schema
 * and integrates with the VehicleListingDrawer flow.
 *
 * Architecture:
 * - Uses h_listings table from h_schema for unified listing management
 * - Supports both rental and fractional ownership listings
 * - Handles vehicle creation and listing creation in a single transaction
 * - Integrates with the existing VehicleListingDrawer -> ListingTermsDrawer flow
 *
 * Schema Tables Used:
 * - h_listings: Main listings table with JSON details
 * - h_listing_approval_status: Tracks approval workflow
 * - vehicles: Vehicle records
 * - vehicleModel, vehicleMake: Vehicle specifications
 * - party: User/owner information
 */

import { db } from "../db";
import { eq, and, desc } from "drizzle-orm";
import {
  vehicles,
  vehicleModel,
  vehicleMake,
  party,
  vehicleMedia,
} from "../drizzle/schema";
import {
  h_listings,
  h_listing_approval_status,
} from "../drizzle/h_schema/listings";
import { getUserAttributes } from "@/lib/serverUserAttributes";

// ==================== TYPES ====================

// Vehicle data from VehicleListingDrawer
export interface VehicleListingData {
  listingType: "rental" | "fractional" | "";
  rentalPurpose?: "business" | "individual";
  rentalPeriod?: "daily" | "weekly" | "monthly";
  ownershipPercentage?: string;
  allowPartialPurchase?: boolean;
  minimumPurchasePercentage?: string;
  make: string;
  model: string;
  year: string;
  color: string;
  mileage: string;
  condition: string;
  images: string[]; // S3 paths instead of File objects
  documents: DocumentUploadData[];
}

// Document upload data structure
export interface DocumentUploadData {
  name: string;
  uploaded: boolean;
  required: boolean;
  s3Path?: string; // S3 path for database storage
}

// Financial terms from ListingTermsDrawer
export interface FinancialTermsData {
  rentalRate?: number;
  deposit?: number;
  fractionalPrice?: number;
  minimumInvestment?: number;
  expectedReturns?: string;
  paymentTerms?: string;
}

// Applicant requirements from ListingTermsDrawer
export interface ApplicantRequirements {
  minAge?: number;
  drivingExperienceYears?: number;
  gender?: "male" | "female" | "any";
  creditScore?: string;
  employmentStatus?: string;
  monthlyIncome?: number;
}

// Complete listing submission data
export interface CreateListingRequest {
  vehicleData: VehicleListingData;
  financialTerms: FinancialTermsData;
  requirements: ApplicantRequirements;
  // Additional fields for vehicle creation
  makeId?: number;
  modelId?: number;
  vinNumber?: string;
  vehicleRegistration?: string;
  countryId?: number;
}

// Response type for listing creation
export interface CreateListingResponse {
  success: boolean;
  listingId?: number;
  vehicleId?: number;
  message: string;
  error?: string;
}

// ==================== HELPER FUNCTIONS ====================

/**
 * Find or create vehicle model based on make/model names
 */
async function findOrCreateVehicleModel(
  tx: any,
  makeName: string,
  modelName: string,
  year: string
): Promise<{ makeId: number; modelId: number }> {
  // Find make
  let make = await tx
    .select()
    .from(vehicleMake)
    .where(eq(vehicleMake.name, makeName))
    .limit(1);

  if (make.length === 0) {
    // Create make if it doesn't exist
    make = await tx
      .insert(vehicleMake)
      .values({
        name: makeName,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning();
  }

  const makeId = make[0].id;

  // Find model
  let model = await tx
    .select()
    .from(vehicleModel)
    .where(
      and(eq(vehicleModel.makeId, makeId), eq(vehicleModel.model, modelName))
    )
    .limit(1);

  if (model.length === 0) {
    // Create model if it doesn't exist
    model = await tx
      .insert(vehicleModel)
      .values({
        makeId: makeId,
        model: modelName,
        firstYear: parseInt(year),
        lastYear: parseInt(year),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning();
  }

  return { makeId, modelId: model[0].id };
}

/**
 * Create listing details JSON based on listing type
 */
function createListingDetailsJson(
  listingType: string,
  vehicleData: VehicleListingData,
  financialTerms: FinancialTermsData,
  requirements: ApplicantRequirements
): string {
  const baseDetails = {
    applicantPreferences: {
      minAge: requirements.minAge || 25,
      drivingExperienceYears: requirements.drivingExperienceYears || 2,
      gender: requirements.gender || "any",
      creditScore: requirements.creditScore,
      employmentStatus: requirements.employmentStatus,
      monthlyIncome: requirements.monthlyIncome,
    },
  };

  if (listingType === "rental") {
    return JSON.stringify({
      ...baseDetails,
      rate: financialTerms.rentalRate || 0,
      period: vehicleData.rentalPeriod || "daily",
      deposit: financialTerms.deposit || 0,
      purpose: vehicleData.rentalPurpose || "individual",
      paymentTerms: financialTerms.paymentTerms,
    });
  } else if (listingType === "fractional") {
    return JSON.stringify({
      ...baseDetails,
      fraction: parseFloat(vehicleData.ownershipPercentage || "25") / 100,
      amount: financialTerms.fractionalPrice || 0,
      minimumInvestment: financialTerms.minimumInvestment || 0,
      expectedReturns: financialTerms.expectedReturns,
      allowPartialPurchase: vehicleData.allowPartialPurchase || false,
      minimumPurchasePercentage: vehicleData.minimumPurchasePercentage,
    });
  }

  throw new Error(`Unsupported listing type: ${listingType}`);
}

// ==================== MAIN FUNCTIONS ====================

/**
 * Create a comprehensive vehicle listing with approval workflow
 * This function integrates with the VehicleListingDrawer -> ListingTermsDrawer flow
 */
export async function createVehicleListing(
  request: CreateListingRequest
): Promise<CreateListingResponse> {
  console.log("🚀 [createVehicleListing] Starting vehicle listing creation...");
  console.log(
    "📝 [createVehicleListing] Input request keys:",
    Object.keys(request)
  );

  try {
    // Get user authentication
    console.log("🔐 [createVehicleListing] Getting user attributes...");
    const userAttributes = await getUserAttributes();
    console.log("👤 [createVehicleListing] User attributes:", userAttributes);

    const { ["custom:db_id"]: dbId } = userAttributes || {};
    if (!dbId) {
      console.error(
        "❌ [createVehicleListing] User not authenticated - no custom:db_id"
      );
      return {
        success: false,
        message: "User not authenticated",
        error: "AUTHENTICATION_REQUIRED",
      };
    }

    const partyId = parseInt(dbId);
    console.log("🆔 [createVehicleListing] Using partyId:", partyId);

    // Input validation
    const { vehicleData, financialTerms, requirements } = request;
    console.log(
      "✅ [createVehicleListing] Destructured request data - vehicleData keys:",
      Object.keys(vehicleData || {})
    );
    console.log(
      "✅ [createVehicleListing] Destructured request data - financialTerms keys:",
      Object.keys(financialTerms || {})
    );
    console.log(
      "✅ [createVehicleListing] Destructured request data - requirements keys:",
      Object.keys(requirements || {})
    );

    console.log("🔍 [createVehicleListing] Validating required fields...");
    if (
      !vehicleData.listingType ||
      !vehicleData.make ||
      !vehicleData.model ||
      !vehicleData.year
    ) {
      console.error(
        "❌ [createVehicleListing] Missing required vehicle data:",
        {
          listingType: vehicleData.listingType,
          make: vehicleData.make,
          model: vehicleData.model,
          year: vehicleData.year,
        }
      );
      return {
        success: false,
        message: "Missing required vehicle information",
        error: "VALIDATION_ERROR",
      };
    }
    console.log("✅ [createVehicleListing] Required fields validation passed");

    if (!request.vinNumber) {
      return {
        success: false,
        message: "VIN number is required",
        error: "VALIDATION_ERROR",
      };
    }

    // Validate financial terms based on listing type
    if (vehicleData.listingType === "rental" && !financialTerms.rentalRate) {
      return {
        success: false,
        message: "Rental rate is required for rental listings",
        error: "VALIDATION_ERROR",
      };
    }

    if (
      vehicleData.listingType === "fractional" &&
      !financialTerms.fractionalPrice
    ) {
      return {
        success: false,
        message:
          "Fractional price is required for fractional ownership listings",
        error: "VALIDATION_ERROR",
      };
    }

    // Execute in transaction for data consistency
    console.log("🔄 [createVehicleListing] Starting database transaction...");
    const result = await db.transaction(async (tx) => {
      console.log("🔍 [createVehicleListing] Verifying party exists...");
      // Verify party exists
      const partyExists = await tx
        .select({ id: party.id })
        .from(party)
        .where(eq(party.id, partyId))
        .limit(1);

      if (partyExists.length === 0) {
        console.error(
          `❌ [createVehicleListing] Party with ID ${partyId} does not exist`
        );
        throw new Error(`Party with ID ${partyId} does not exist`);
      }
      console.log("✅ [createVehicleListing] Party verification passed");

      // Find or create vehicle model
      console.log(
        "🔍 [createVehicleListing] Finding or creating vehicle model..."
      );
      const { makeId, modelId } = await findOrCreateVehicleModel(
        tx,
        vehicleData.make,
        vehicleData.model,
        vehicleData.year
      );
      console.log("✅ [createVehicleListing] Vehicle model found/created:", {
        makeId,
        modelId,
      });

      console.log(
        `🚗 [createVehicleListing] Creating vehicle with partyId: ${partyId}, modelId: ${modelId}`
      );

      // Create vehicle
      const vehicleData_insert = {
        partyId: partyId,
        modelId: modelId,
        vinNumber: request.vinNumber!,
        vehicleRegistration: request.vehicleRegistration || null,
        countryId: request.countryId || 1,
        manufacturingYear: parseInt(vehicleData.year),
        purchaseDate: new Date().toISOString(),
        color: vehicleData.color || null,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      console.log(
        "📝 [createVehicleListing] Vehicle insert data:",
        vehicleData_insert
      );

      const newVehicle = await tx
        .insert(vehicles)
        .values(vehicleData_insert)
        .returning();

      console.log(
        `✅ [createVehicleListing] Created vehicle with ID: ${newVehicle[0].id}`
      );

      // Add vehicle images if provided (following existing listings.ts pattern)
      if (vehicleData.images && vehicleData.images.length > 0) {
        console.log("📸 [createVehicleListing] Adding vehicle media...");
        const mediaInserts = vehicleData.images.map((imagePath) => ({
          vehicleId: newVehicle[0].id,
          mediaPath: imagePath,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }));

        await tx.insert(vehicleMedia).values(mediaInserts);
        console.log(
          `✅ [createVehicleListing] Inserted ${mediaInserts.length} vehicle media records`
        );
      }

      // Create listing details JSON
      console.log("📋 [createVehicleListing] Creating listing details JSON...");
      const listingDetailsJson = createListingDetailsJson(
        vehicleData.listingType,
        vehicleData,
        financialTerms,
        requirements
      );
      console.log(
        "📋 [createVehicleListing] Listing details JSON:",
        listingDetailsJson
      );

      // Map listing type to h_schema enum values
      const mappedListingType:
        | "rental"
        | "fractional"
        | "ehailing-lease"
        | "ehailing-rent" =
        vehicleData.listingType === "rental" ? "rental" : "fractional";

      console.log(
        `📝 [createVehicleListing] Creating listing with type: ${mappedListingType}`
      );

      // Create listing in h_listings table
      const listingInsertData = {
        partyId: partyId,
        sourceType: "vehicle" as const, // This is a vehicle-based listing
        sourceId: newVehicle[0].id, // References the vehicle
        listingType: mappedListingType,
        effectiveFrom: new Date().toISOString(),
        effectiveTo: new Date(
          Date.now() + 365 * 24 * 60 * 60 * 1000
        ).toISOString(), // 1 year from now
        listingDetails: listingDetailsJson,
      };
      console.log(
        "📝 [createVehicleListing] Listing insert data:",
        listingInsertData
      );

      const newListing = await tx
        .insert(h_listings)
        .values(listingInsertData)
        .returning();

      console.log(
        `✅ [createVehicleListing] Created listing with ID: ${newListing[0].id}`
      );

      // Create initial approval status (pending)
      console.log("📋 [createVehicleListing] Creating approval status...");
      await tx.insert(h_listing_approval_status).values({
        listingId: newListing[0].id,
        status: "pending",
        reason: "New listing submitted for review",
        statusAt: new Date().toISOString(),
        statusBy: null, // Will be set by admin when reviewed
      });

      console.log(
        `✅ [createVehicleListing] Created approval status for listing ${newListing[0].id}`
      );

      const transactionResult = {
        vehicleId: newVehicle[0].id,
        listingId: newListing[0].id,
      };
      console.log(
        "🎉 [createVehicleListing] Transaction completed successfully:",
        transactionResult
      );

      return transactionResult;
    });

    console.log(
      "🎉 [createVehicleListing] Vehicle listing creation completed successfully"
    );
    return {
      success: true,
      listingId: result.listingId,
      vehicleId: result.vehicleId,
      message:
        "Vehicle listing created successfully and submitted for approval",
    };
  } catch (error) {
    console.error(
      "❌ [createVehicleListing] Error creating vehicle listing:",
      error
    );
    console.error(
      "❌ [createVehicleListing] Error stack:",
      error instanceof Error ? error.stack : "No stack trace"
    );
    return {
      success: false,
      message: "Failed to create vehicle listing",
      error: error instanceof Error ? error.message : "UNKNOWN_ERROR",
    };
  }
}

/**
 * Get listing by ID with full details
 */
export async function getListingById(listingId: number) {
  try {
    const listing = await db
      .select({
        // Listing details
        id: h_listings.id,
        partyId: h_listings.partyId,
        sourceType: h_listings.sourceType,
        sourceId: h_listings.sourceId,
        listingType: h_listings.listingType,
        effectiveFrom: h_listings.effectiveFrom,
        effectiveTo: h_listings.effectiveTo,
        listingDetails: h_listings.listingDetails,
        // Vehicle details
        vehicleVin: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        vehicleColor: vehicles.color,
        vehicleYear: vehicles.manufacturingYear,
        // Model and make details
        modelName: vehicleModel.model,
        makeName: vehicleMake.name,
        // Approval status
        approvalStatus: h_listing_approval_status.status,
        approvalReason: h_listing_approval_status.reason,
        approvalStatusAt: h_listing_approval_status.statusAt,
      })
      .from(h_listings)
      .leftJoin(vehicles, eq(h_listings.sourceId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .leftJoin(
        h_listing_approval_status,
        eq(h_listings.id, h_listing_approval_status.listingId)
      )
      .where(eq(h_listings.id, listingId))
      .orderBy(desc(h_listing_approval_status.statusAt))
      .limit(1);

    if (listing.length === 0) {
      return null;
    }

    const record = listing[0];

    return {
      id: record.id,
      partyId: record.partyId,
      sourceType: record.sourceType,
      sourceId: record.sourceId,
      listingType: record.listingType,
      effectiveFrom: record.effectiveFrom,
      effectiveTo: record.effectiveTo,
      listingDetails: JSON.parse(record.listingDetails),
      vehicle: {
        vin: record.vehicleVin,
        registration: record.vehicleRegistration,
        color: record.vehicleColor,
        year: record.vehicleYear,
        model: record.modelName,
        make: record.makeName,
      },
      approvalStatus: record.approvalStatus,
      approvalReason: record.approvalReason,
      approvalStatusAt: record.approvalStatusAt,
    };
  } catch (error) {
    console.error("Error fetching listing:", error);
    throw error;
  }
}

/**
 * Get user's listings with approval status
 */
export async function getUserListings() {
  try {
    const userAttributes = await getUserAttributes();
    if (!userAttributes?.party_id) {
      throw new Error("User not authenticated");
    }

    const partyId = parseInt(userAttributes.party_id);

    const listings = await db
      .select({
        id: h_listings.id,
        listingType: h_listings.listingType,
        effectiveFrom: h_listings.effectiveFrom,
        effectiveTo: h_listings.effectiveTo,
        listingDetails: h_listings.listingDetails,
        // Vehicle details
        vehicleVin: vehicles.vinNumber,
        vehicleColor: vehicles.color,
        vehicleYear: vehicles.manufacturingYear,
        modelName: vehicleModel.model,
        makeName: vehicleMake.name,
        // Latest approval status
        approvalStatus: h_listing_approval_status.status,
        approvalStatusAt: h_listing_approval_status.statusAt,
      })
      .from(h_listings)
      .leftJoin(vehicles, eq(h_listings.sourceId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .leftJoin(
        h_listing_approval_status,
        eq(h_listings.id, h_listing_approval_status.listingId)
      )
      .where(eq(h_listings.partyId, partyId))
      .orderBy(desc(h_listings.effectiveFrom));

    return listings.map((record) => ({
      id: record.id,
      listingType: record.listingType,
      effectiveFrom: record.effectiveFrom,
      effectiveTo: record.effectiveTo,
      listingDetails: JSON.parse(record.listingDetails),
      vehicle: {
        vin: record.vehicleVin,
        color: record.vehicleColor,
        year: record.vehicleYear,
        model: record.modelName,
        make: record.makeName,
      },
      approvalStatus: record.approvalStatus,
      approvalStatusAt: record.approvalStatusAt,
    }));
  } catch (error) {
    console.error("Error fetching user listings:", error);
    throw error;
  }
}
