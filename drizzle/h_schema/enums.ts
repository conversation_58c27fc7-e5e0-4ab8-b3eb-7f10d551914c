import { pgEnum } from "drizzle-orm/pg-core";

export const applicationStatusEnum = pgEnum("application_status", [
  "pending",
  "under_review",
  "approved",
  "rejected",
  "withdrawn",
]);

export const listingTypeEnum = pgEnum("listing_type", [
  "rental",
  "fractional",
  // "lease-to-own", This mostly applied when <PERSON>ly did not accept external parties to lease vehicles for ehailing via Poolly
  "ehailing-lease", // Poolly inventory e-hailing with lease-to-own
  "ehailing-rent", // Non Poolly e-hailing with rental option only
]);

export const listingSourceTypeEnum = pgEnum("listing_source_type", [
  "catalog",
  "vehicle",
]);

export const documentStatusEnum = pgEnum("document_status", [
  "pending",
  "uploaded",
  "verified",
  "rejected",
]);

export const listingStatusEnum = pgEnum("listing_status", [
  "draft",
  "published",
  "archived",
]);

export const listingApprovalStatusEnum = pgEnum("listing_approval_status", [
  "pending",
  "under_review",
  "approved",
  "rejected",
  "withdrawn",
]);

export const listingPublishStatusEnum = pgEnum("listing_publish_status", [
  "pending",
  "published",
  "archived",
]);

export const listingStatsTypeEnum = pgEnum("listing_stats_type", [
  "views",
  "applications",
]);
