import { sql } from "drizzle-orm";
import { pgTable, text, serial, timestamp, integer } from "drizzle-orm/pg-core";
import { party } from "../schema";
import {
  listingApprovalStatusEnum,
  listingSourceTypeEnum,
  listingPublishStatusEnum,
  listingStatusEnum,
  listingTypeEnum,
  listingStatsTypeEnum,
} from "./enums";
import { temporalFields } from "./temporals";

// Unified Listings Table - handles both catalog and vehicle listings with no nulls
export const h_listings = pgTable("h_listings", {
  id: serial().primaryKey().notNull(),
  partyId: integer("party_id") // Change to integer if party.id is serial
    .notNull()
    .references(() => party.id),

  // No nulls approach
  sourceType: listingSourceTypeEnum("source_type").notNull(), // 'catalog' or 'vehicle'
  sourceId: integer("source_id").notNull(), // References either vehicleCatalog.id or vehicles.id

  listingType: listingTypeEnum("listing_type").notNull(),
  // audience: audienceenum().notNull(),

  // Availability window
  effectiveFrom: timestamp("effective_from", {
    withTimezone: true,
    mode: "string",
  }).notNull(),
  effectiveTo: timestamp("effective_to", {
    withTimezone: true,
    mode: "string",
  }).default(sql`'infinity'::timestamp`),

  // Type-specific data as JSON
  listingDetails: text("listing_details").notNull(), // JSON string
  /* Examples:
   * Catalog (E-hailing): {"rate": 2700, "type": weekly, "initiationFee": 7500, applicantPreferences: {"minAge": 25, "drivingExperienceYears": 5, "gender": "female"}}
   * Vehicle (Rental): {"rate": 500, "type": "daily", "deposit": 5000}
   * Vehicle (Fractional): {"fraction": 0.25, "amount": 50000, applicantPreferences: {"minAge": 25, "drivingExperienceYears": 5, "gender": "female"}}
   */

  // ...temporalFields,
});

export const h_listing_stats = pgTable("h_listing_stats", {
  id: serial().primaryKey().notNull(),
  listingId: integer("listing_id") // Change to integer since h_listings.id is serial
    .notNull()
    .references(() => h_listings.id),
  statsType: listingStatsTypeEnum("stats_type").notNull(),
  createdAt: timestamp("created_at", {
    withTimezone: true,
    mode: "string",
  }).notNull(),
  partyId: integer("party_id").notNull(), // Change to integer if party.id is serial
});

export const h_listing_approval_status = pgTable("h_listing_approval_status", {
  id: serial().primaryKey().notNull(),
  listingId: integer("listing_id") // Change to integer since h_listings.id is serial
    .notNull()
    .references(() => h_listings.id),
  status: listingApprovalStatusEnum("status").notNull(),
  reason: text("reason"),
  statusAt: timestamp("status_at", {
    withTimezone: true,
    mode: "string",
  }).notNull(),
  statusBy: integer("status_by").references(() => party.id), // Change to integer if party.id is serial
});

export const h_listing_publish_status = pgTable("h_listing_publish_status", {
  id: serial().primaryKey().notNull(),
  listingId: integer("listing_id") // Change to integer since h_listings.id is serial
    .notNull()
    .references(() => h_listings.id),
  status: listingPublishStatusEnum("status").notNull(),
  statusAt: timestamp("status_at", {
    withTimezone: true,
    mode: "string",
  }).notNull(),
  statusBy: integer("status_by").references(() => party.id), // Change to integer if party.id is serial
});
